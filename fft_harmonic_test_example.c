/**
 * @file fft_harmonic_test_example.c
 * @brief FFT谐波分析功能测试示例
 * @note 这是一个示例文件，展示如何使用新增的FFT谐波分析功能
 */

#include "fft.h"
#include "usart.h"
#include "delay.h"

/**
 * @brief 测试函数1：模拟信号谐波分析
 * @note 生成一个包含基波和谐波的测试信号，进行FFT分析
 */
void test_harmonic_analysis_with_simulated_signal(void)
{
    printf("=== TEST 1: SIMULATED SIGNAL HARMONIC ANALYSIS ===\r\n");
    
    // 模拟一个包含基波(1kHz)和谐波(2kHz, 3kHz)的信号
    float test_signal[FFT_LENGTH];
    float fundamental_freq = 1000.0f;  // 1kHz基波
    float sample_rate = 815534.0f;     // 采样率
    
    // 生成测试信号：基波 + 2次谐波 + 3次谐波
    for(int i = 0; i < FFT_LENGTH; i++)
    {
        float t = (float)i / sample_rate;
        test_signal[i] = 1.0f * sinf(2 * PI * fundamental_freq * t) +        // 基波
                        0.3f * sinf(2 * PI * 2 * fundamental_freq * t) +      // 2次谐波
                        0.1f * sinf(2 * PI * 3 * fundamental_freq * t);       // 3次谐波
        
        // 转换为ADC值范围 (0-4095)
        test_signal[i] = (test_signal[i] + 1.5f) * (4095.0f / 3.0f);
        if(test_signal[i] < 0) test_signal[i] = 0;
        if(test_signal[i] > 4095) test_signal[i] = 4095;
    }
    
    // 将测试信号复制到FFT输入缓冲区
    for(int i = 0; i < FFT_LENGTH; i++)
    {
        fft_inputbuf[2*i] = test_signal[i] * (3.3f / 4096.0f);  // 转换为电压
        fft_inputbuf[2*i+1] = 0.0f;  // 虚部为0
    }
    
    // 执行FFT
    arm_cfft_radix4_f32(&scfft, fft_inputbuf);
    arm_cmplx_mag_f32(fft_inputbuf, fft_outputbuf, FFT_LENGTH);
    
    // 调用谐波分析函数
    analyze_harmonics_and_output(fft_outputbuf, FFT_LENGTH, sample_rate);
    
    printf("=== END OF TEST 1 ===\r\n\r\n");
}

/**
 * @brief 测试函数2：实际ADC数据谐波分析
 * @note 使用实际的ADC采样数据进行谐波分析
 */
void test_harmonic_analysis_with_adc_data(void)
{
    printf("=== TEST 2: REAL ADC DATA HARMONIC ANALYSIS ===\r\n");
    
    // 检查是否有有效的FFT输出数据
    if(fft_outputbuf == NULL)
    {
        printf("Error: FFT output buffer is not available\r\n");
        return;
    }
    
    // 检查FFT输出缓冲区是否包含有效数据
    float max_value = 0.0f;
    for(int i = 1; i < FFT_LENGTH/2; i++)
    {
        if(fft_outputbuf[i] > max_value)
        {
            max_value = fft_outputbuf[i];
        }
    }
    
    if(max_value < 0.001f)  // 如果最大值太小，说明可能没有有效信号
    {
        printf("Warning: FFT output seems to contain no significant signal\r\n");
        printf("Max amplitude found: %.6f\r\n", max_value);
    }
    
    // 使用现有的FFT结果进行谐波分析
    output_fft_harmonics_to_uart();
    
    printf("=== END OF TEST 2 ===\r\n\r\n");
}

/**
 * @brief 测试函数3：比较两种分析方法
 * @note 比较基于现有结果和直接分析的两种方法
 */
void test_compare_analysis_methods(void)
{
    printf("=== TEST 3: COMPARE ANALYSIS METHODS ===\r\n");
    
    printf("Method 1: Using existing FFT results\r\n");
    output_fft_harmonics_to_uart();
    
    delay_ms(100);  // 短暂延时
    
    printf("Method 2: Direct FFT buffer analysis\r\n");
    analyze_harmonics_and_output(fft_outputbuf, FFT_LENGTH, sampfre);
    
    printf("=== END OF TEST 3 ===\r\n\r\n");
}

/**
 * @brief 测试函数4：不同频率分辨率的影响
 * @note 测试不同FFT长度对频率分辨率的影响
 */
void test_frequency_resolution_effects(void)
{
    printf("=== TEST 4: FREQUENCY RESOLUTION EFFECTS ===\r\n");
    
    // 显示当前FFT参数
    printf("Current FFT Parameters:\r\n");
    printf("  FFT Length: %d points\r\n", FFT_LENGTH);
    printf("  Sample Rate: %.1f Hz\r\n", sampfre);
    printf("  Frequency Resolution: %.2f Hz\r\n", sampfre / FFT_LENGTH);
    printf("  Nyquist Frequency: %.1f Hz\r\n", sampfre / 2.0f);
    printf("  Maximum Detectable Harmonic Order: %d\r\n", 
           (int)(sampfre / 2.0f / 1000.0f));  // 假设基波为1kHz
    
    // 分析当前数据
    analyze_harmonics_and_output(fft_outputbuf, FFT_LENGTH, sampfre);
    
    printf("=== END OF TEST 4 ===\r\n\r\n");
}

/**
 * @brief 主测试函数
 * @note 依次执行所有测试
 */
void run_all_harmonic_tests(void)
{
    printf("\r\n");
    printf("########################################\r\n");
    printf("#     FFT HARMONIC ANALYSIS TESTS     #\r\n");
    printf("########################################\r\n");
    printf("\r\n");
    
    // 测试1：模拟信号
    test_harmonic_analysis_with_simulated_signal();
    delay_ms(500);
    
    // 测试2：实际ADC数据
    test_harmonic_analysis_with_adc_data();
    delay_ms(500);
    
    // 测试3：比较两种方法
    test_compare_analysis_methods();
    delay_ms(500);
    
    // 测试4：频率分辨率影响
    test_frequency_resolution_effects();
    
    printf("########################################\r\n");
    printf("#        ALL TESTS COMPLETED          #\r\n");
    printf("########################################\r\n");
    printf("\r\n");
}

/**
 * @brief 快速测试函数
 * @note 只执行基本的谐波分析，适合在主循环中调用
 */
void quick_harmonic_test(void)
{
    printf("=== QUICK HARMONIC TEST ===\r\n");
    
    // 检查FFT输出缓冲区
    if(fft_outputbuf != NULL)
    {
        analyze_harmonics_and_output(fft_outputbuf, FFT_LENGTH, sampfre);
    }
    else
    {
        printf("Error: FFT output buffer not available\r\n");
    }
    
    printf("=== END OF QUICK TEST ===\r\n\r\n");
}
