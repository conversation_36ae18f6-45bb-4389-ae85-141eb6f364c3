# FFT基波及谐波分析串口输出功能实现总结

## 实现概述

本次实现为STM32F4项目添加了三个主要功能：
1. **FFT基波及有效谐波的频率和电压幅度串口输出功能** - 实时分析信号的频谱特性，并通过UART串口输出详细的谐波分析结果
2. **基波频率大于10kHz时自动输出标准正弦波功能** - 当检测到高频基波时，自动通过DAC输出对应频率的标准正弦波
3. **IFFT后DAC输出频率与FFT基波频率匹配功能** - 确保IFFT重构后的DAC输出频率与FFT检测到的基波频率完全一致

## 文件修改清单

### 1. 核心功能文件

#### HARDWARE/FFT/fft.h
- **修改内容**：
  - 添加 `MAX_HARMONICS` 宏定义
  - 添加 `fft_harmonic_config.h` 包含
  - 新增函数声明：
    - `output_fft_harmonics_to_uart()`
    - `analyze_harmonics_and_output()`
    - `check_and_output_standard_sine()`
    - `output_standard_sine_wave()`

#### HARDWARE/FFT/fft.c
- **修改内容**：
  - 实现 `output_fft_harmonics_to_uart()` 函数
  - 实现 `analyze_harmonics_and_output()` 函数
  - 实现 `check_and_output_standard_sine()` 函数
  - 实现 `output_standard_sine_wave()` 函数
  - 基于现有FFT分析结果的谐波输出
  - 直接分析FFT缓冲区的谐波检测
  - THD（总谐波失真）计算
  - 基波频率>10kHz时自动输出标准正弦波
  - 包含DAC头文件以支持正弦波输出

#### HARDWARE/ADC/adc.c
- **修改内容**：
  - 在 `QCZ_FFT1()` 函数中添加自动谐波分析输出调用
  - 添加基于FFT检测频率的DAC输出功能（默认禁用）
  - 包含DAC和配置头文件
  - 支持传统FFT后的频率匹配DAC输出（可配置，默认关闭）

#### USER/main.c
- **修改内容**：
  - 在 `ADC3_ProcessFFT()` 函数中添加谐波分析调用
  - 添加组合按键（PE4+PE3）触发FFT测试功能
  - 添加组合按键（PE2+PE3）触发标准正弦波测试功能
  - 新增 `combo_key_pressed` 变量
  - 更新按键功能提示显示
  - 标准正弦波测试包含多个频率（15kHz, 20kHz, 25kHz, 30kHz）
  - 实现 `ADC3_ReconstructSignalWithFrequency()` 函数
  - 实现 `ADC3_StartDACOutputWithFrequency()` 函数
  - 基于FFT检测频率的精确IFFT重构和DAC输出

### 2. 新增配置文件

#### fft_harmonic_config.h
- **功能**：谐波分析和标准正弦波输出的配置系统
- **包含内容**：
  - 基本配置参数（谐波数量、阈值等）
  - 串口输出配置（格式、精度等）
  - 标准正弦波输出配置
    - `ENABLE_AUTO_STANDARD_SINE_OUTPUT`: 启用/禁用自动输出
    - `AUTO_SINE_OUTPUT_THRESHOLD_HZ`: 触发阈值（默认10kHz）
  - 辅助宏定义

### 3. 示例和文档文件

#### fft_harmonic_test_example.c
- **功能**：完整的测试示例代码
- **包含测试**：
  - 模拟信号谐波分析
  - 实际ADC数据分析
  - 两种分析方法比较
  - 频率分辨率影响测试

#### FFT_HARMONIC_ANALYSIS_README.md
- **功能**：FFT谐波分析功能使用说明文档
- **内容**：使用方法、输出格式、技术参数等

#### STANDARD_SINE_OUTPUT_README.md
- **功能**：标准正弦波输出功能使用说明文档
- **内容**：自动触发条件、手动测试方法、配置选项等

#### IFFT_FREQUENCY_MATCHING_README.md
- **功能**：IFFT频率匹配功能使用说明文档
- **内容**：频率匹配原理、使用方法、技术参数等

## 核心功能特性

### 1. 双重分析模式
- **模式1**：基于现有FFT结果的快速分析
- **模式2**：直接分析FFT缓冲区的完整分析

### 2. 智能谐波检测
- 自动寻找基波（最大幅度峰值）
- 在基波整数倍频率附近搜索谐波
- 可配置的幅度阈值过滤
- 支持最多10个谐波的检测

### 3. 详细输出信息
- 基波频率、幅度、频率bin索引
- 各次谐波的频率、幅度、相对比例
- THD（总谐波失真）计算
- 频率分辨率和采样参数信息

### 4. 自动标准正弦波输出
- **触发条件**：基波频率 > 10kHz（可配置）
- **输出信号**：与检测频率相同的标准正弦波
- **自动启用**：自动启用DAC输出功能
- **频率范围**：0.1Hz - 100kHz

### 5. IFFT频率匹配
- **精确重构**：基于检测到的基波频率进行IFFT重构
- **频率一致性**：确保DAC输出频率与FFT检测频率完全一致
- **智能参数计算**：自动计算最优DAC采样率和缓冲区参数
- **误差控制**：频率误差通常<1%

### 6. 多种触发方式
- **自动触发**：FFT处理完成后自动输出谐波分析
- **自动正弦波**：高频基波检测后自动输出标准正弦波
- **自动频率匹配**：IFFT后自动输出频率匹配的信号
- **手动FFT测试**：组合按键（PE4+PE3）触发FFT测试
- **手动正弦波测试**：组合按键（PE2+PE3）触发正弦波测试
- **程序调用**：可在代码中任意位置调用相关函数

## 技术参数

### FFT参数支持
- **传统FFT**：4096点，815534Hz采样率，199.15Hz频率分辨率
- **ADC3 FFT**：512点，102314Hz采样率，199.83Hz频率分辨率

### 谐波检测精度
- **频率精度**：受FFT频率分辨率限制
- **幅度精度**：6位小数精度
- **相对精度**：2位小数百分比
- **搜索容差**：±2个频率bin

### 串口输出
- **波特率**：112500
- **输出格式**：结构化文本格式
- **实时性**：分析完成后立即输出

## 使用方法

### 1. 自动模式
- **谐波分析**：系统在执行FFT分析时会自动输出谐波信息
- **标准正弦波**：当检测到基波频率>10kHz时自动输出标准正弦波
- **IFFT频率匹配**：ADC3模式下自动进行频率匹配的IFFT重构和DAC输出

### 2. 手动测试
- **FFT测试**：同时按下PE4和PE3键，进行FFT谐波分析测试
- **正弦波测试**：同时按下PE2和PE3键，进行标准正弦波输出测试

### 3. 程序调用
```c
// FFT谐波分析
output_fft_harmonics_to_uart();  // 使用现有FFT结果
analyze_harmonics_and_output(fft_outputbuf, FFT_LENGTH, sampfre);  // 直接分析

// 标准正弦波输出
check_and_output_standard_sine();  // 检查并自动输出
output_standard_sine_wave(15000.0f);  // 输出指定频率

// IFFT频率匹配
ADC3_ReconstructSignalWithFrequency(1000.0f);  // 基于频率重构
ADC3_StartDACOutputWithFrequency(1000.0f);     // 频率匹配输出
```

## 输出示例

```
=== DIRECT FFT HARMONIC ANALYSIS ===
Sample Frequency: 102314.0 Hz
FFT Length: 512 points
Frequency Resolution: 199.83 Hz

FUNDAMENTAL FREQUENCY:
Bin: 5, Frequency: 999.15 Hz, Amplitude: 0.123456

SIGNIFICANT HARMONICS:
Order	Bin	Frequency(Hz)	Amplitude	Ratio(%)
2	10	1998.30		0.012345	10.00
3	15	2997.45		0.006789	5.50

Calculated THD: 12.34%
Harmonics Found: 2
=== END OF DIRECT HARMONIC ANALYSIS ===
```

## 配置选项

通过 `fft_harmonic_config.h` 可以配置：

### 谐波分析配置
- 最大谐波检测数量（默认10个）
- 幅度阈值百分比（默认5%）
- 输出格式和精度
- 频率搜索容差

### 标准正弦波输出配置
- `ENABLE_AUTO_STANDARD_SINE_OUTPUT`：启用/禁用自动输出（默认启用）
- `AUTO_SINE_OUTPUT_THRESHOLD_HZ`：触发阈值（默认10000Hz）
- 最小/最大基波频率限制

### IFFT频率匹配配置
- `ENABLE_IFFT_DAC_FREQUENCY_MATCHING`：启用/禁用IFFT频率匹配（默认启用）
- `ENABLE_TRADITIONAL_FFT_DAC_OUTPUT`：启用/禁用传统FFT后DAC输出（默认禁用）
- 频率范围和精度控制

## 性能影响

### 内存使用
- **额外内存**：几乎为零（主要使用现有缓冲区）
- **代码空间**：约2KB

### 处理时间
- **快速模式**：约1-2ms（基于现有结果）
- **完整模式**：约5-10ms（直接分析）
- **串口输出**：约10-20ms（取决于谐波数量）

### 实时性
- 不影响主要的FFT处理流程
- 串口输出在FFT分析完成后进行
- 可通过配置选择快速模式

## 扩展性

### 1. 算法扩展
- 支持添加更精确的频率插值算法
- 可扩展相位分析功能
- 支持自适应阈值算法

### 2. 输出格式扩展
- 支持CSV、JSON等格式输出
- 可添加图形化显示接口
- 支持数据记录和存储

### 3. 应用扩展
- 可用于电机谐波分析
- 电源质量监测
- 音频信号分析
- 振动分析等领域

## 注意事项

1. **频率限制**：谐波频率不能超过奈奎斯特频率
2. **信号质量**：需要足够的信噪比才能准确检测谐波
3. **采样同步**：确保信号采样与分析的同步性
4. **内存安全**：确保FFT缓冲区的有效性
5. **串口速度**：大量输出可能影响实时性

## 调试建议

1. 使用串口调试助手查看输出
2. 通过组合按键随时测试功能
3. 观察THD值评估信号质量
4. 根据需要调整配置参数
5. 使用测试示例验证功能
