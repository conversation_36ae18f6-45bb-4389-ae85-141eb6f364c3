# FFT基波及谐波分析串口输出功能

## 功能概述

本项目新增了FFT基波及有效谐波的频率和电压幅度串口输出功能，可以通过UART串口实时查看信号的频谱分析结果。

## 新增功能

### 1. 自动谐波分析输出
- **函数**: `output_fft_harmonics_to_uart()`
- **功能**: 基于现有的FFT分析结果，输出基波和前10个谐波的信息
- **调用位置**: 在`QCZ_FFT1()`函数中自动调用

### 2. 直接FFT缓冲区分析
- **函数**: `analyze_harmonics_and_output()`
- **功能**: 直接分析FFT输出缓冲区，找出基波和谐波
- **调用位置**: 在`ADC3_ProcessFFT()`函数中调用，也可手动触发

## 使用方法

### 方法1：自动输出（传统FFT）
当系统执行传统的4096点FFT分析时（通过`QCZ_FFT1`函数），会自动通过串口输出谐波分析结果。

### 方法2：ADC3处理时自动输出
1. 按下PE2键或选择"PROC ON"按钮启动ADC3处理
2. 系统会自动进行512点FFT分析
3. 完成后自动输出谐波分析结果到串口

### 方法3：手动触发测试
1. 同时按下PE4和PE3键
2. 系统会使用当前的FFT输出缓冲区进行谐波分析
3. 结果通过串口输出

## 串口输出格式

### 输出示例1（基于现有FFT结果）
```
=== FFT HARMONIC ANALYSIS ===
Sample Frequency: 815534.0 Hz
FFT Length: 4096 points
Frequency Resolution: 199.15 Hz

FUNDAMENTAL FREQUENCY:
Frequency: 1000.00 Hz
Amplitude: 0.123456 V
Bin Index: 5

HARMONICS (1st to 9th):
Order	Frequency(Hz)	Amplitude(V)	Ratio(%)
1	1000.00		0.123456	100.00
2	2000.00		0.012345	10.00
3	3000.00		0.006789	5.50
...

THD (Total Harmonic Distortion): 12.34%
=== END OF HARMONIC ANALYSIS ===
```

### 输出示例2（直接分析FFT缓冲区）
```
=== DIRECT FFT HARMONIC ANALYSIS ===
Sample Frequency: 102314.0 Hz
FFT Length: 512 points
Frequency Resolution: 199.83 Hz
Amplitude Threshold: 0.006172 (5% of fundamental)

FUNDAMENTAL FREQUENCY:
Bin: 5, Frequency: 999.15 Hz, Amplitude: 0.123456

SIGNIFICANT HARMONICS:
Order	Bin	Frequency(Hz)	Amplitude	Ratio(%)
2	10	1998.30		0.012345	10.00
3	15	2997.45		0.006789	5.50
...

Calculated THD: 12.34%
Harmonics Found: 3
=== END OF DIRECT HARMONIC ANALYSIS ===
```

## 技术参数

### FFT参数
- **传统FFT**: 4096点，采样频率815534Hz，频率分辨率199.15Hz
- **ADC3 FFT**: 512点，采样频率102314Hz，频率分辨率199.83Hz

### 谐波检测
- **最大谐波数**: 10个
- **幅度阈值**: 基波幅度的5%
- **频率搜索范围**: ±2个频率bin
- **THD计算**: 基于2-10次谐波的RMS值

### 串口设置
- **波特率**: 112500
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无

## 代码修改说明

### 新增文件内容
1. **fft.h**: 添加了`MAX_HARMONICS`宏定义和两个新函数声明
2. **fft.c**: 实现了两个谐波分析函数
3. **adc.c**: 在`QCZ_FFT1`函数中添加了自动输出调用
4. **main.c**: 在`ADC3_ProcessFFT`函数中添加了分析调用，并增加了组合按键触发功能

### 主要函数
- `output_fft_harmonics_to_uart()`: 基于现有FFT结果的谐波输出
- `analyze_harmonics_and_output()`: 直接分析FFT缓冲区的谐波输出

## 注意事项

1. **内存使用**: 新增功能主要使用现有的FFT缓冲区，内存开销很小
2. **实时性**: 串口输出会占用一定时间，建议在非实时关键路径中使用
3. **精度**: 频率精度受FFT长度和采样频率限制
4. **谐波检测**: 只检测基波的整数倍频率，适用于周期性信号分析

## 调试建议

1. 使用串口调试助手查看输出结果
2. 可以通过组合按键随时触发测试
3. 观察THD值来评估信号质量
4. 根据需要调整幅度阈值（当前为5%）
