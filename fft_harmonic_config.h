/**
 * @file fft_harmonic_config.h
 * @brief FFT谐波分析配置文件
 * @note 包含谐波分析的可配置参数
 */

#ifndef __FFT_HARMONIC_CONFIG_H
#define __FFT_HARMONIC_CONFIG_H

// ================================
// 基本配置参数
// ================================

/**
 * @brief 最大谐波检测数量
 * @note 包括基波在内的总数量，实际谐波数量为此值减1
 */
#ifndef MAX_HARMONICS
#define MAX_HARMONICS 10
#endif

/**
 * @brief 谐波幅度阈值百分比
 * @note 相对于基波幅度的百分比，低于此值的谐波将被忽略
 * @range 1-50 (1%-50%)
 */
#define HARMONIC_AMPLITUDE_THRESHOLD_PERCENT 5

/**
 * @brief 频率搜索容差（单位：频率bin）
 * @note 在理论谐波频率附近搜索实际峰值的范围
 * @range 1-5
 */
#define HARMONIC_FREQUENCY_TOLERANCE_BINS 2

/**
 * @brief 最小基波频率（Hz）
 * @note 低于此频率的峰值不会被认为是基波
 */
#define MIN_FUNDAMENTAL_FREQUENCY 10.0f

/**
 * @brief 最大基波频率（Hz）
 * @note 高于此频率的峰值不会被认为是基波
 * @note 设置为0表示不限制（使用奈奎斯特频率）
 */
#define MAX_FUNDAMENTAL_FREQUENCY 0.0f

// ================================
// 串口输出配置
// ================================

/**
 * @brief 启用详细输出模式
 * @note 1: 输出详细信息，0: 输出简化信息
 */
#define HARMONIC_VERBOSE_OUTPUT 1

/**
 * @brief 启用THD计算
 * @note 1: 计算并输出THD，0: 跳过THD计算
 */
#define HARMONIC_ENABLE_THD_CALCULATION 1

/**
 * @brief 启用相位信息输出
 * @note 1: 输出相位信息，0: 只输出幅度信息
 * @warning 相位计算需要复数FFT结果，确保fft_inputbuf可用
 */
#define HARMONIC_ENABLE_PHASE_OUTPUT 0

/**
 * @brief 数值输出精度（小数点后位数）
 */
#define HARMONIC_FREQUENCY_PRECISION 2  // 频率精度
#define HARMONIC_AMPLITUDE_PRECISION 6  // 幅度精度
#define HARMONIC_RATIO_PRECISION 2      // 比例精度
#define HARMONIC_THD_PRECISION 2        // THD精度

// ================================
// 高级配置参数
// ================================

/**
 * @brief 启用窗函数补偿
 * @note 1: 对窗函数造成的幅度损失进行补偿，0: 不补偿
 */
#define HARMONIC_ENABLE_WINDOW_COMPENSATION 1

/**
 * @brief 窗函数增益补偿系数
 * @note 汉宁窗的理论增益约为0.5，补偿系数为2.0
 */
#define HARMONIC_WINDOW_GAIN_COMPENSATION 2.0f

/**
 * @brief 启用谐波频率精确计算
 * @note 1: 使用插值方法精确计算频率，0: 使用bin中心频率
 */
#define HARMONIC_ENABLE_PRECISE_FREQUENCY 0

/**
 * @brief 启用自适应阈值
 * @note 1: 根据噪声水平自动调整阈值，0: 使用固定阈值
 */
#define HARMONIC_ENABLE_ADAPTIVE_THRESHOLD 0

/**
 * @brief 噪声水平估计范围（频率bin）
 * @note 用于自适应阈值计算的噪声估计范围
 */
#define HARMONIC_NOISE_ESTIMATION_BINS 50

// ================================
// 输出格式配置
// ================================

/**
 * @brief 输出格式选择
 * @note 0: 标准格式，1: CSV格式，2: JSON格式
 */
#define HARMONIC_OUTPUT_FORMAT 0

/**
 * @brief CSV格式分隔符
 */
#define HARMONIC_CSV_SEPARATOR ","

/**
 * @brief 启用输出标题
 * @note 1: 输出表格标题，0: 只输出数据
 */
#define HARMONIC_ENABLE_OUTPUT_HEADER 1

// ================================
// 性能优化配置
// ================================

/**
 * @brief 启用快速模式
 * @note 1: 跳过一些精度较高但耗时的计算，0: 完整计算
 */
#define HARMONIC_ENABLE_FAST_MODE 0

/**
 * @brief 最大处理频率bin数
 * @note 限制处理的频率范围以提高性能，0表示处理全部
 */
#define HARMONIC_MAX_PROCESSING_BINS 0

/**
 * @brief 启用缓存优化
 * @note 1: 使用缓存减少重复计算，0: 每次重新计算
 */
#define HARMONIC_ENABLE_CACHE_OPTIMIZATION 0

// ================================
// 调试配置
// ================================

/**
 * @brief 启用调试输出
 * @note 1: 输出调试信息，0: 不输出调试信息
 */
#define HARMONIC_DEBUG_OUTPUT 0

/**
 * @brief 启用性能测量
 * @note 1: 测量并输出处理时间，0: 不测量
 */
#define HARMONIC_ENABLE_PERFORMANCE_MEASUREMENT 0

/**
 * @brief 启用数据验证
 * @note 1: 验证输入数据的有效性，0: 跳过验证
 */
#define HARMONIC_ENABLE_DATA_VALIDATION 1

// ================================
// 兼容性配置
// ================================

/**
 * @brief 兼容旧版本输出格式
 * @note 1: 保持与旧版本相同的输出格式，0: 使用新格式
 */
#define HARMONIC_LEGACY_OUTPUT_COMPATIBILITY 0

/**
 * @brief 使用浮点数学库
 * @note 1: 使用标准数学库，0: 使用ARM CMSIS DSP库
 */
#define HARMONIC_USE_STANDARD_MATH 1

// ================================
// 错误处理配置
// ================================

/**
 * @brief 启用错误恢复
 * @note 1: 尝试从错误中恢复，0: 遇到错误时停止
 */
#define HARMONIC_ENABLE_ERROR_RECOVERY 1

/**
 * @brief 最大错误重试次数
 */
#define HARMONIC_MAX_ERROR_RETRIES 3

// ================================
// 配置验证宏
// ================================

// 验证配置参数的有效性
#if MAX_HARMONICS < 2 || MAX_HARMONICS > 50
#error "MAX_HARMONICS must be between 2 and 50"
#endif

#if HARMONIC_AMPLITUDE_THRESHOLD_PERCENT < 1 || HARMONIC_AMPLITUDE_THRESHOLD_PERCENT > 50
#error "HARMONIC_AMPLITUDE_THRESHOLD_PERCENT must be between 1 and 50"
#endif

#if HARMONIC_FREQUENCY_TOLERANCE_BINS < 1 || HARMONIC_FREQUENCY_TOLERANCE_BINS > 10
#error "HARMONIC_FREQUENCY_TOLERANCE_BINS must be between 1 and 10"
#endif

// ================================
// 辅助宏定义
// ================================

/**
 * @brief 计算幅度阈值
 * @param fundamental_amplitude 基波幅度
 * @return 阈值
 */
#define CALCULATE_AMPLITUDE_THRESHOLD(fundamental_amplitude) \
    ((fundamental_amplitude) * (HARMONIC_AMPLITUDE_THRESHOLD_PERCENT) / 100.0f)

/**
 * @brief 检查频率是否在有效范围内
 * @param freq 频率值
 * @param max_freq 最大频率（奈奎斯特频率）
 * @return 1: 有效，0: 无效
 */
#define IS_VALID_FREQUENCY(freq, max_freq) \
    ((freq) >= MIN_FUNDAMENTAL_FREQUENCY && \
     ((MAX_FUNDAMENTAL_FREQUENCY == 0.0f) ? ((freq) <= (max_freq)) : ((freq) <= MAX_FUNDAMENTAL_FREQUENCY)))

#endif /* __FFT_HARMONIC_CONFIG_H */
