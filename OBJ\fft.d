..\obj\fft.o: ..\HARDWARE\FFT\fft.c
..\obj\fft.o: ..\HARDWARE\FFT\fft.h
..\obj\fft.o: ..\SYSTEM\sys\sys.h
..\obj\fft.o: ..\USER\stm32f4xx.h
..\obj\fft.o: ..\CORE\core_cm4.h
..\obj\fft.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\fft.o: ..\CORE\core_cmInstr.h
..\obj\fft.o: ..\CORE\core_cmFunc.h
..\obj\fft.o: ..\CORE\core_cm4_simd.h
..\obj\fft.o: ..\USER\system_stm32f4xx.h
..\obj\fft.o: ..\USER\stm32f4xx_conf.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\fft.o: ..\USER\stm32f4xx.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\fft.o: ..\FWLIB\inc\misc.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\fft.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\fft.o: ..\HARDWARE\ADC\adc.h
..\obj\fft.o: ..\SYSTEM\delay\delay.h
..\obj\fft.o: ..\HARDWARE\FFT\fft.h
..\obj\fft.o: ..\DSP_LIB\Include\arm_math.h
..\obj\fft.o: ..\DSP_LIB\Include\core_cm4.h
..\obj\fft.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
..\obj\fft.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
..\obj\fft.o: ..\SYSTEM\usart\usart.h
..\obj\fft.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
