#include "fft.h"
#include "kalman.h"
#include "led.h"
#include "math.h"
#include "stdio.h"

arm_cfft_radix4_instance_f32 scfft;
float fft_inputbuf[FFT_LENGTH*2];
float fft_outputbuf[FFT_LENGTH];
float sampfre;
u8 flag3;
float Adresult = 0,thd=0;
float k;
float frequency = 0;
u8  u,  Vpp_buff[20] = {0},fre[20]= {0},ele[20]= {0};
float elec;
int set_right;
float set_rightk=1;
allvpp_fre all_vpp_fre;
u8 len;
float effective_value[6];
float n[2043];
int m[2043];

uint32_t peak1_idx;
uint32_t peak2_idx;
uint32_t length=FFT_LENGTH;

// ����: fft_outputbuf�����������飩, length�����鳤�ȣ���4096��
// ���: peak1_idx����һ����ֵ��������, peak2_idx���ڶ�����ֵ��������
void find_peak_indices(float* magnitude, uint32_t length, uint32_t* peak1_idx, uint32_t* peak2_idx) {
    *peak1_idx = 0;
    *peak2_idx = 0;
    float peak1_val = 0;
    float peak2_val = 0;

    // ֻ���ǰһ��Ƶ�㣨ʵ���źŶԳ��ԣ�
    for (uint32_t i = 1; i < length/2 - 1; i++) {
        // ���ֲ���ֵ������������ֱ��������
        if (magnitude[i] > magnitude[i-1] && magnitude[i] > magnitude[i+1]) {
            if (magnitude[i] > peak1_val) {
                // ���·�ֵ2Ϊ�ɵķ�ֵ1
                peak2_val = peak1_val;
                *peak2_idx = *peak1_idx;
                // ���·�ֵ1
                peak1_val = magnitude[i];
                *peak1_idx = i;
            } else if (magnitude[i] > peak2_val) {
                // ���·�ֵ2
                peak2_val = magnitude[i];
                *peak2_idx = i;
            }
        }
    }
}

void FFT(__IO uint16_t* buff)//ֵ���������fft
{
    static u16 i =0;
    for(i=0; i<FFT_LENGTH; i++) //�����ź�����
    {
        fft_inputbuf[2*i]=(float32_t) buff[i]*(3.3/4096);
        fft_inputbuf[2*i+1]=0;
    }
    arm_cfft_radix4_f32(&scfft,fft_inputbuf);	//FFT���㣨��4��
    arm_cmplx_mag_f32(fft_inputbuf,fft_outputbuf,FFT_LENGTH);	//��������������ģ�÷�ֵ
    find_peak_indices(fft_outputbuf,length,&peak1_idx,&peak2_idx);
}


float Hanningwindow(int t)//������
{
    float wt;
    wt=(1-cos(2*PI*t/FFT_LENGTH))/2;
    return wt;
}



float get_pianyik(u16 time) //����ں�������˫�����߲�ֵ��У��ϵ��k
{
    float k;
    if(fft_outputbuf[time+1]>=fft_outputbuf[time-1])
    {
        k=(fft_outputbuf[time+1]*2-fft_outputbuf[time])/(fft_outputbuf[time+1]+fft_outputbuf[time]);
    }
    else
    {
        k=(fft_outputbuf[time]-fft_outputbuf[time-1]*2)/(fft_outputbuf[time]+fft_outputbuf[time-1]);
    }
    return k;
}



modulus_point Get_basevpp_point(float32_t* fft_outputbuf)//ͨ���Ƚϴ�С�õ�������ģֵ����
{
    modulus_point first;
    u16 i;
    float max=0;
    for(i=5; i<(FFT_LENGTH/2); i++)
    {
        if(fft_outputbuf[i]>max)
        {
            max=fft_outputbuf[i];
            first.time=i;
        }
    }
    first.fftvpp_modulus=max;
    return first;
}


u16 timef;
void get_basefrevpp(void)//�õ������ķ�����Ƶ��
{

    timef=Get_basevpp_point(fft_outputbuf).time;//�õ��������ڵ���
    k=get_pianyik(timef);
    Adresult=(PI*k*fft_outputbuf[timef]*2*(1-k*k)/sin(PI*k))*4/FFT_LENGTH;//�õ�vpp,vppУ��,���ں�����
//    elec=Adresult/5;
    frequency =((timef+k)*sampfre/FFT_LENGTH);//�õ�Ƶ��  Ƶ��У�� ���ں�����p
 
}



modulus_point Get_othervpp_point(modulus_point* first,float32_t* fft_outputbuf,u16 n)//�õ���������г����ģֵ����
{
    modulus_point other;
    other.time=(first->time)*n;
    other.fftvpp_modulus=fft_outputbuf[(first->time)*n];
    return other;
}


vpp_fre Get_vpp_fre(modulus_point* other) //ͨ��ģֵ���ŵõ����������г���ķ�����Ƶ��
{

    vpp_fre anyvppfre;
    float k=get_pianyik(other->time);
    if(other->time>0)
    {
        anyvppfre.vpp=(PI*k*fft_outputbuf[other->time]*2*(1-k*k)/sin(PI*k))*4/FFT_LENGTH*set_rightk*2.2;
//		anyvppfre.fre=((float)(time)+k)*sampfre/FFT_LENGTH;
    }
    else
    {
        anyvppfre.vpp=(other->fftvpp_modulus)/FFT_LENGTH;
        anyvppfre.fre=(other->time)*sampfre/FFT_LENGTH;
    }
    return anyvppfre;
}


allvpp_fre n_get_vppfre(float32_t* fft_outputbuf) //���������ǰʮ��г����ֵ
{
    u8 i;
    modulus_point first;
    modulus_point other;
    vpp_fre one_vpp_fre;
    allvpp_fre all_vpp_fre;
    float fre_base;

    first=Get_basevpp_point(fft_outputbuf);
    fre_base=(first.time+k)*sampfre/FFT_LENGTH;
    for(i=1; i<10; i++)
    {
        other=Get_othervpp_point(&first,fft_outputbuf,i);
        one_vpp_fre=Get_vpp_fre(&other);
        all_vpp_fre.fre[i]=fre_base*i;
        all_vpp_fre.vpp[i]=one_vpp_fre.vpp;
        //all_vpp_fre.vpp[i]=kalman(i,all_vpp_fre.vpp[i]);
    }
    return all_vpp_fre;
}



void get_thd(void) //��THDֵ��г��ʧ��
{
    u8 i=0;

    for(i=1; i<6; i++)
    {
        effective_value[i]=(double)(all_vpp_fre.vpp[i])/2;
    }
//	thd=sqrt((effective_value[2]/effective_value[1])*(effective_value[2]/effective_value[1])+
//	(effective_value[3]/effective_value[1])*(effective_value[2]/effective_value[1]));
    thd=sqrt(effective_value[2]*effective_value[2]+
             effective_value[3]*effective_value[3]+
             effective_value[4]*effective_value[4]+
             effective_value[5]*effective_value[5])/effective_value[1]*100-0.2;
    thd=kalman_thd(thd);
}

/**
 * @brief  串口输出FFT基波及有效谐波的频率和电压幅度
 * @param  None
 * @retval None
 * @note   调用现有的FFT分析函数，通过串口输出基波和谐波信息
 */
void output_fft_harmonics_to_uart(void)
{
    // 获取基波信息
    get_basefrevpp();

    // 获取前10个谐波信息
    all_vpp_fre = n_get_vppfre(fft_outputbuf);

    // 计算THD
    get_thd();

    // 输出标题
    printf("=== FFT HARMONIC ANALYSIS ===\r\n");
    printf("Sample Frequency: %.1f Hz\r\n", sampfre);
    printf("FFT Length: %d points\r\n", FFT_LENGTH);
    printf("Frequency Resolution: %.2f Hz\r\n", sampfre/FFT_LENGTH);
    printf("\r\n");

    // 输出基波信息
    printf("FUNDAMENTAL FREQUENCY:\r\n");
    printf("Frequency: %.2f Hz\r\n", frequency);
    printf("Amplitude: %.6f V\r\n", Adresult);
    printf("Bin Index: %d\r\n", timef);
    printf("\r\n");

    // 输出谐波信息
    printf("HARMONICS (1st to %dth):\r\n", MAX_HARMONICS-1);
    printf("Order\tFrequency(Hz)\tAmplitude(V)\tRatio(%%)\r\n");

    for(u8 i = 1; i < MAX_HARMONICS; i++)
    {
        if(all_vpp_fre.fre[i] > 0 && all_vpp_fre.vpp[i] > 0)
        {
            // 计算相对于基波的百分比
            float ratio_percent = 0.0f;
            if(Adresult > 0)
            {
                ratio_percent = (all_vpp_fre.vpp[i] / Adresult) * 100.0f;
            }

            printf("%d\t%.2f\t\t%.6f\t%.2f\r\n",
                   i,
                   all_vpp_fre.fre[i],
                   all_vpp_fre.vpp[i],
                   ratio_percent);
        }
    }

    printf("\r\n");
    printf("THD (Total Harmonic Distortion): %.2f%%\r\n", thd);
    printf("=== END OF HARMONIC ANALYSIS ===\r\n\r\n");
}

/**
 * @brief  分析FFT输出并通过串口输出谐波信息
 * @param  fft_outputbuf: FFT幅度谱输出缓冲区
 * @param  fft_length: FFT长度
 * @param  sample_freq: 采样频率
 * @retval None
 * @note   直接分析FFT输出缓冲区，找出基波和谐波，输出频率和幅度信息
 */
void analyze_harmonics_and_output(float32_t* fft_outputbuf, uint32_t fft_length, float sample_freq)
{
    if(fft_outputbuf == NULL || fft_length == 0 || sample_freq <= 0)
    {
        printf("Error: Invalid parameters for harmonic analysis\r\n");
        return;
    }

    // 计算频率分辨率
    float freq_resolution = sample_freq / fft_length;

    // 寻找基波（最大峰值，排除直流分量）
    float max_amplitude = 0.0f;
    uint32_t fundamental_bin = 0;

    // 从bin 1开始搜索（跳过直流分量bin 0）
    // 搜索范围：1 Hz 到 奈奎斯特频率
    uint32_t search_start = (uint32_t)(1.0f / freq_resolution);
    uint32_t search_end = fft_length / 2;  // 奈奎斯特频率对应的bin

    if(search_start < 1) search_start = 1;

    for(uint32_t i = search_start; i < search_end; i++)
    {
        if(fft_outputbuf[i] > max_amplitude)
        {
            max_amplitude = fft_outputbuf[i];
            fundamental_bin = i;
        }
    }

    if(fundamental_bin == 0 || max_amplitude <= 0)
    {
        printf("Error: No valid fundamental frequency found\r\n");
        return;
    }

    // 计算基波频率
    float fundamental_freq = fundamental_bin * freq_resolution;

    // 设置幅度阈值（基波幅度的5%）
    float amplitude_threshold = max_amplitude * 0.05f;

    // 输出分析结果
    printf("=== DIRECT FFT HARMONIC ANALYSIS ===\r\n");
    printf("Sample Frequency: %.1f Hz\r\n", sample_freq);
    printf("FFT Length: %d points\r\n", fft_length);
    printf("Frequency Resolution: %.2f Hz\r\n", freq_resolution);
    printf("Amplitude Threshold: %.6f (5%% of fundamental)\r\n", amplitude_threshold);
    printf("\r\n");

    // 输出基波信息
    printf("FUNDAMENTAL FREQUENCY:\r\n");
    printf("Bin: %d, Frequency: %.2f Hz, Amplitude: %.6f\r\n",
           fundamental_bin, fundamental_freq, max_amplitude);
    printf("\r\n");

    // 寻找并输出谐波
    printf("SIGNIFICANT HARMONICS:\r\n");
    printf("Order\tBin\tFrequency(Hz)\tAmplitude\tRatio(%%)\r\n");

    uint8_t harmonic_count = 0;

    // 搜索谐波（基波的整数倍）
    for(uint8_t harmonic_order = 2; harmonic_order <= MAX_HARMONICS && harmonic_count < MAX_HARMONICS; harmonic_order++)
    {
        // 计算理论谐波频率对应的bin
        uint32_t harmonic_bin = (uint32_t)(harmonic_order * fundamental_bin);

        // 检查是否超出奈奎斯特频率
        if(harmonic_bin >= fft_length / 2)
        {
            break;
        }

        // 在理论bin附近搜索峰值（允许±2个bin的误差）
        float harmonic_amplitude = 0.0f;
        uint32_t actual_bin = harmonic_bin;

        for(int offset = -2; offset <= 2; offset++)
        {
            uint32_t search_bin = harmonic_bin + offset;
            if(search_bin > 0 && search_bin < fft_length / 2)
            {
                if(fft_outputbuf[search_bin] > harmonic_amplitude)
                {
                    harmonic_amplitude = fft_outputbuf[search_bin];
                    actual_bin = search_bin;
                }
            }
        }

        // 只输出超过阈值的谐波
        if(harmonic_amplitude > amplitude_threshold)
        {
            float actual_freq = actual_bin * freq_resolution;
            float ratio_percent = (harmonic_amplitude / max_amplitude) * 100.0f;

            printf("%d\t%d\t%.2f\t\t%.6f\t%.2f\r\n",
                   harmonic_order,
                   actual_bin,
                   actual_freq,
                   harmonic_amplitude,
                   ratio_percent);

            harmonic_count++;
        }
    }

    // 计算简化的THD（基于找到的谐波）
    float thd_sum = 0.0f;
    for(uint8_t harmonic_order = 2; harmonic_order <= MAX_HARMONICS; harmonic_order++)
    {
        uint32_t harmonic_bin = (uint32_t)(harmonic_order * fundamental_bin);
        if(harmonic_bin >= fft_length / 2) break;

        float harmonic_amplitude = 0.0f;
        for(int offset = -2; offset <= 2; offset++)
        {
            uint32_t search_bin = harmonic_bin + offset;
            if(search_bin > 0 && search_bin < fft_length / 2)
            {
                if(fft_outputbuf[search_bin] > harmonic_amplitude)
                {
                    harmonic_amplitude = fft_outputbuf[search_bin];
                }
            }
        }

        if(harmonic_amplitude > amplitude_threshold)
        {
            thd_sum += harmonic_amplitude * harmonic_amplitude;
        }
    }

    float calculated_thd = 0.0f;
    if(max_amplitude > 0)
    {
        calculated_thd = (sqrtf(thd_sum) / max_amplitude) * 100.0f;
    }

    printf("\r\n");
    printf("Calculated THD: %.2f%%\r\n", calculated_thd);
    printf("Harmonics Found: %d\r\n", harmonic_count);
    printf("=== END OF DIRECT HARMONIC ANALYSIS ===\r\n\r\n");
}



