# FFT基波频率大于10kHz时自动输出标准正弦波功能

## 功能概述

本功能在FFT检测到基波频率大于10kHz时，自动通过DAC输出一个标准的正弦波信号。这对于高频信号的重现和测试非常有用。

## 功能特性

### 1. 自动触发
- **触发条件**: 当FFT检测到的基波频率 > 10kHz时自动触发
- **输出信号**: 与检测到的基波频率相同的标准正弦波
- **自动启用**: 如果DAC未启用，系统会自动启用DAC输出

### 2. 可配置参数
通过`fft_harmonic_config.h`可以配置：
- `ENABLE_AUTO_STANDARD_SINE_OUTPUT`: 启用/禁用自动输出功能
- `AUTO_SINE_OUTPUT_THRESHOLD_HZ`: 触发阈值（默认10000Hz）

### 3. 手动测试功能
- **按键组合**: 同时按下PE2+PE3键
- **测试频率**: 自动测试15kHz, 20kHz, 25kHz, 30kHz
- **测试时长**: 每个频率输出2秒

## 使用方法

### 方法1：自动触发
1. 确保配置文件中`ENABLE_AUTO_STANDARD_SINE_OUTPUT`设置为1
2. 进行FFT分析（通过ADC采样或其他方式）
3. 当检测到基波频率>10kHz时，系统自动输出标准正弦波

### 方法2：手动测试
1. 同时按下PE2和PE3键
2. 系统会依次输出不同频率的标准正弦波
3. 通过串口查看测试结果

### 方法3：程序调用
```c
// 直接调用函数输出指定频率的标准正弦波
output_standard_sine_wave(15000.0f);  // 输出15kHz正弦波

// 检查当前基波频率并自动输出（如果>阈值）
check_and_output_standard_sine();
```

## 串口输出示例

### 自动触发输出
```
=== FFT HARMONIC ANALYSIS ===
Sample Frequency: 815534.0 Hz
FFT Length: 4096 points
Frequency Resolution: 199.15 Hz

FUNDAMENTAL FREQUENCY:
Frequency: 15000.00 Hz
Amplitude: 0.123456 V
Bin Index: 75

=== FUNDAMENTAL FREQUENCY > 10000 Hz DETECTED ===
Detected Frequency: 15000.00 Hz
Triggering standard sine wave output...
Auto-enabling DAC for standard sine output
Generating standard sine wave at 15000.00 Hz
Standard sine wave output started at 15000.00 Hz

Technical Parameters:
  Output Type: Standard Sine Wave
  Frequency: 15000.00 Hz
  Amplitude: Auto-adjusted based on frequency
  DAC Resolution: 12-bit (0-4095)
  Output Range: 0-3.3V
  Max Frequency: 100000 Hz

=== STANDARD SINE WAVE OUTPUT ACTIVATED ===
```

### 手动测试输出
```
=== STANDARD SINE WAVE TEST TRIGGERED ===
Testing standard sine wave output at different frequencies...
Testing frequency: 15000 Hz
Generating standard sine wave at 15000.00 Hz
Standard sine wave output started at 15000.00 Hz
Testing frequency: 20000 Hz
Generating standard sine wave at 20000.00 Hz
Standard sine wave output started at 20000.00 Hz
...
```

## 技术参数

### 频率范围
- **最小频率**: 0.1 Hz
- **最大频率**: 100 kHz（由DAC_MAX_FREQ_HZ定义）
- **触发阈值**: 10 kHz（可配置）

### 输出特性
- **DAC分辨率**: 12位（0-4095）
- **输出电压范围**: 0-3.3V
- **中心电压**: 1.65V
- **波形类型**: 标准正弦波
- **幅度调整**: 根据频率自动调整

### 查找表参数
- **采样点数**: 256点/周期
- **精度**: 高精度浮点计算
- **更新方式**: 实时计算和更新

## 配置选项

### 基本配置
```c
// 启用自动标准正弦波输出功能
#define ENABLE_AUTO_STANDARD_SINE_OUTPUT 1

// 触发阈值（Hz）
#define AUTO_SINE_OUTPUT_THRESHOLD_HZ 10000.0f
```

### 高级配置
可以通过修改DAC相关参数来调整输出特性：
- `DAC_SINE_SAMPLES`: 正弦波查找表大小
- `DAC_MAX_FREQ_HZ`: 最大输出频率
- `DAC_OFFSET_VOLTAGE`: 偏移电压

## 应用场景

### 1. 高频信号重现
- 当输入信号频率较高时，自动生成标准参考信号
- 用于信号质量对比和分析

### 2. 测试和校准
- 生成已知频率的标准信号
- 用于系统校准和验证

### 3. 信号处理研究
- 提供纯净的正弦波信号
- 用于算法测试和验证

## 注意事项

### 1. 频率限制
- 输出频率不能超过100kHz
- 超出范围的频率会被拒绝并显示错误信息

### 2. DAC负载
- 高频输出可能对DAC造成较大负载
- 建议适当的外部滤波和缓冲

### 3. 系统性能
- 频繁的DAC更新可能影响系统实时性
- 建议在非关键路径中使用

### 4. 电路考虑
- 确保DAC输出电路能够处理高频信号
- 考虑信号完整性和EMI问题

## 调试建议

### 1. 串口监控
- 使用串口调试助手查看详细输出信息
- 观察频率检测和输出状态

### 2. 示波器验证
- 使用示波器验证输出波形质量
- 检查频率准确性和波形失真

### 3. 参数调整
- 根据实际需求调整触发阈值
- 优化DAC输出参数

### 4. 性能测试
- 测试不同频率下的输出质量
- 验证系统稳定性

## 故障排除

### 1. 无输出信号
- 检查DAC是否正确初始化
- 验证用户使能状态
- 确认频率在有效范围内

### 2. 频率不准确
- 检查定时器配置
- 验证查找表生成
- 确认采样率计算

### 3. 波形失真
- 检查DAC负载
- 验证外部电路
- 调整幅度参数

## 扩展功能

### 1. 多频率输出
- 可扩展为同时输出多个频率
- 支持谐波合成

### 2. 相位控制
- 添加相位调整功能
- 支持相位锁定

### 3. 幅度控制
- 更精确的幅度控制
- 支持幅度调制

这个功能为高频信号处理提供了强大的工具，特别适用于需要标准参考信号的应用场景。
