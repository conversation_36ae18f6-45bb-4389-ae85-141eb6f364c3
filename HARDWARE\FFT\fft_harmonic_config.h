/**
 * @file fft_harmonic_config.h
 * @brief FFT谐波分析配置文件
 * @note 包含谐波分析的基本配置参数
 */

#ifndef __FFT_HARMONIC_CONFIG_H
#define __FFT_HARMONIC_CONFIG_H

// ================================
// 基本配置参数
// ================================

/**
 * @brief 最大谐波检测数量
 * @note 包括基波在内的总数量，实际谐波数量为此值减1
 */
#ifndef MAX_HARMONICS
#define MAX_HARMONICS 10
#endif

/**
 * @brief 谐波幅度阈值百分比
 * @note 相对于基波幅度的百分比，低于此值的谐波将被忽略
 */
#define HARMONIC_AMPLITUDE_THRESHOLD_PERCENT 5

/**
 * @brief 频率搜索容差（单位：频率bin）
 * @note 在理论谐波频率附近搜索实际峰值的范围
 */
#define HARMONIC_FREQUENCY_TOLERANCE_BINS 2

/**
 * @brief 最小基波频率（Hz）
 */
#define MIN_FUNDAMENTAL_FREQUENCY 10.0f

/**
 * @brief 最大基波频率（Hz）
 * @note 设置为0表示不限制（使用奈奎斯特频率）
 */
#define MAX_FUNDAMENTAL_FREQUENCY 0.0f

/**
 * @brief 启用自动标准正弦波输出
 * @note 1: 当基波频率>10kHz时自动输出标准正弦波，0: 禁用此功能
 */
#define ENABLE_AUTO_STANDARD_SINE_OUTPUT 1

/**
 * @brief 自动输出标准正弦波的频率阈值（Hz）
 * @note 当检测到的基波频率超过此值时，自动输出标准正弦波
 */
#define AUTO_SINE_OUTPUT_THRESHOLD_HZ 10000.0f

/**
 * @brief 启用IFFT后DAC输出与基波频率匹配
 * @note 1: IFFT后DAC输出频率与FFT检测的基波频率相同，0: 使用原有逻辑
 */
#define ENABLE_IFFT_DAC_FREQUENCY_MATCHING 1

/**
 * @brief 启用传统FFT后的频率匹配DAC输出
 * @note 1: 传统FFT后自动输出与基波频率相同的DAC正弦波，0: 禁用
 */
#define ENABLE_TRADITIONAL_FFT_DAC_OUTPUT 0

// ================================
// 输出配置
// ================================

/**
 * @brief 启用THD计算
 */
#define HARMONIC_ENABLE_THD_CALCULATION 1

/**
 * @brief 数值输出精度（小数点后位数）
 */
#define HARMONIC_FREQUENCY_PRECISION 2  // 频率精度
#define HARMONIC_AMPLITUDE_PRECISION 6  // 幅度精度
#define HARMONIC_RATIO_PRECISION 2      // 比例精度
#define HARMONIC_THD_PRECISION 2        // THD精度

// ================================
// 辅助宏定义
// ================================

/**
 * @brief 计算幅度阈值
 */
#define CALCULATE_AMPLITUDE_THRESHOLD(fundamental_amplitude) \
    ((fundamental_amplitude) * (HARMONIC_AMPLITUDE_THRESHOLD_PERCENT) / 100.0f)

#endif /* __FFT_HARMONIC_CONFIG_H */
