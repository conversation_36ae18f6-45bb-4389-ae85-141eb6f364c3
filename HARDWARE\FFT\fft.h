#ifndef __FFT_H
#define __FFT_H
#include "sys.h"
#include "adc.h"
#include "delay.h"
#include "arm_math.h"
#include "usart.h"
#include "fft_harmonic_config.h"  // 包含谐波分析配置

#define FFT_LENGTH 4096 //��������
#define MAX_HARMONICS 10 //最大谐波数量
extern float fft_outputbuf[FFT_LENGTH],fft_inputbuf[FFT_LENGTH*2]; 
extern arm_cfft_radix4_instance_f32 scfft;//fft��ʼ���ṹ��
extern float sampfre;
extern float Adresult,thd;
extern float frequency;
extern u16 timef;
extern uint32_t peak1_idx;
extern uint32_t peak2_idx;
//extern allvpp_fre all_vpp_fre;
typedef struct
{
	float fftvpp_modulus;
	u16 time;
} modulus_point;

typedef struct
{
	float vpp;
	float fre;
} vpp_fre;

typedef struct
{
	float vpp[10];
	float fre[10];
} allvpp_fre;

typedef struct
{
  float vppbuff[FFT_LENGTH];
} all_vpp;


void FFT(__IO uint16_t* buff);
allvpp_fre n_get_vppfre(float32_t* fft_outputbuf);//���������ǰʮ��г����ֵ
modulus_point Get_basevpp_point(float32_t* fft_outputbuf);//ͨ���Ƚϴ�С�õ�������ģֵ����
modulus_point Get_othervpp_point(modulus_point* first,float32_t* fft_outputbuf,u16 n);//�õ���������г����ģֵ����
vpp_fre Get_vpp_fre(modulus_point* other); //ͨ��ģֵ���ŵõ����������г���ķ�����Ƶ��
void getbuffvpp(float32_t* fft_outputbuf,u16 i);//��ģֵ������ȫ��ת���ɷ��ֵ������
float Hanningwindow(int t);//������
float get_pianyik(u16 time);//����ں�������˫�����߲�ֵ��У��ϵ��k
void get_basefrevpp(void);//�õ������ķ�����Ƶ��
void get_thd(void);

// 新增函数：串口输出FFT基波及有效谐波的频率和电压幅度
void output_fft_harmonics_to_uart(void);
void analyze_harmonics_and_output(float32_t* fft_outputbuf, uint32_t fft_length, float sample_freq);

// 新增函数：基波频率大于10kHz时输出标准正弦波
void check_and_output_standard_sine(void);
void output_standard_sine_wave(float fundamental_freq);

#endif /*__FFT_H*/
